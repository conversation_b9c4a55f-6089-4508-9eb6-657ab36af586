# Contributing to this project

Thank you for considering contributing to this project! This document outlines the process for contributing and some guidelines to follow.

Also note, we use the discussions feature in GitHub to have a community to discuss potential ideas, uses, additions and enhancements.

## Code of Conduct

By participating in this project, you agree to abide by our Code of Conduct. Please read it before participating.

## How to Contribute

### Reporting Bugs

- Check if the bug has already been reported in the Issues section
- Include detailed steps to reproduce the bug
- Include any relevant logs or screenshots

### Suggesting Features

- Check if the feature has already been suggested in the Issues section, and consider using the discussions tab in GitHub also. Explain the feature in detail and why it would be valuable.

### Pull Request Process

1. Fork the repository
2. Create a new branch (`git checkout -b feature/your-feature-name`)
3. Make your changes
4. Run any tests or linting to ensure quality
5. Commit your changes with clear, descriptive messages following our commit message convention
6. Push to your branch (`git push origin feature/your-feature-name`)
7. Open a Pull Request against the main branch

## Commit Message Convention

[Commit Convention](./docs/commit.md)

## Code Style

- Follow the existing code style and conventions
- Write clear comments for complex logic
- Ensure all tests pass before submitting

## License

By contributing to this project, you agree that your contributions will be licensed under the same license as the project.
