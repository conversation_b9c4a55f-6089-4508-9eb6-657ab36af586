# Deep Research Phase

Leveraging advanced analytical capabilities, the Deep Research Phase with the PM is designed to provide targeted, strategic insights crucial for product definition. Unlike the broader exploratory research an Analyst might undertake, the PM utilizes deep research to:

- **Validate Product Hypotheses:** Rigorously test assumptions about market need, user problems, and the viability of specific product concepts.
- **Refine Target Audience & Value Proposition:** Gain a nuanced understanding of specific user segments, their precise pain points, and how the proposed product delivers unique value to them.
- **Focused Competitive Analysis:** Analyze competitors through the lens of a specific product idea to identify differentiation opportunities, feature gaps to exploit, and potential market positioning challenges.
- **De-risk PRD Commitments:** Ensure that the problem, proposed solution, and core features are well-understood and validated _before_ detailed planning and resource allocation in the PRD Generation Mode.

Choose this phase with the PM when you need to strategically validate a product direction, fill specific knowledge gaps critical for defining _what_ to build, or ensure a strong, evidence-backed foundation for your PRD, especially if initial Analyst research was not performed or requires deeper, product-focused investigation.

## Purpose

- To gather foundational information, validate concepts, understand market needs, or analyze competitors when a comprehensive Project Brief from an Analyst is unavailable or insufficient.
- To ensure the PM has a solid, data-informed basis for defining a valuable and viable product before committing to PRD specifics.
- To de-risk product decisions by grounding them in targeted research, especially if the user is engaging the PM directly without prior Analyst work or if the initial brief lacks necessary depth.

## Instructions

<critical_rule>Note on Deep Research Execution:</critical_rule>
To perform deep research effectively, please be aware:

- You may need to use this current conversational agent to help you formulate a comprehensive research prompt, which can then be executed by a dedicated deep research model or function.
- Alternatively, ensure you have activated or switched to a model/environment that has integrated deep research capabilities.
  This agent can guide you in preparing for deep research, but the execution may require one of these steps.

1. **Assess Inputs & Identify Gaps:**
    - Review any existing inputs (user's initial idea, high-level requirements, partial brief from Analyst, etc.).
    - Clearly identify critical knowledge gaps concerning:
      - Target audience (needs, pain points, behaviors, key segments).
      - Market landscape (size, trends, opportunities, potential saturation).
      - Competitive analysis (key direct/indirect competitors, their offerings, strengths, weaknesses, market positioning, potential differentiators for this product).
      - Problem/Solution validation (evidence supporting the proposed solution's value and fit for the identified problem).
      - High-level technical or resource considerations (potential major roadblocks or dependencies).
2. **Formulate Research Plan:**
    - Define specific, actionable research questions to address the identified gaps.
    - Propose targeted research activities (e.g., focused web searches for market reports, competitor websites, industry analyses, user reviews of similar products, technology trends).
    - <important_note>Confirm this research plan, scope, and key questions with the user before proceeding with research execution.</important_note>
3. **Execute Research:**
    - Conduct the planned research activities systematically.
    - Prioritize gathering credible, relevant, and actionable insights that directly inform product definition and strategy.
4. **Synthesize & Present Findings:**
    - Organize and summarize key research findings in a clear, concise, and easily digestible manner (e.g., bullet points, brief summaries per research question).
    - Highlight the most critical implications for the product's vision, strategy, target audience, core features, and potential risks.
    - Present these synthesized findings and their implications to the user.
5. **Discussing and Utilizing Research Output:**
    - The comprehensive findings/report from this Deep Research phase can be substantial. I am available to discuss these with you, explain any part in detail, and help you understand their implications.
    - **Options for Utilizing These Findings for PRD Generation:**
      1. **Full Handoff to New PM Session:** The complete research output can serve as a foundational document if you initiate a _new_ session with a Product Manager (PM) agent who will then execute the 'PRD Generate Task'.
      2. **Key Insights Summary for This Session:** I can prepare a concise summary of the most critical findings, tailored to be directly actionable as we (in this current session) transition to potentially invoking the 'PRD Generate Task'.
    - <critical_rule>Regardless of how you proceed, it is highly recommended that these research findings (either the full output or the key insights summary) are provided as direct input when invoking the 'PRD Generate Task'. This ensures the PRD is built upon a solid, evidence-based foundation.</critical_rule>
6. **Confirm Readiness for PRD Generation:**
    - Discuss with the user whether the gathered information provides a sufficient and confident foundation to proceed to the 'PRD Generate Task'.
    - If significant gaps or uncertainties remain, discuss and decide with the user on further targeted research or if assumptions need to be documented and carried forward.
    - Once confirmed, clearly state that the next step could be to invoke the 'PRD Generate Task' or, if applicable, revisit other phase options.
